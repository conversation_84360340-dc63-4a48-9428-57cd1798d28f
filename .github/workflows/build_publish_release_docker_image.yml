name: Docker release build

on:
  release:
    types: [published]

jobs:
    build:
        if: github.repository == 'rclone/rclone'
        runs-on: ubuntu-latest
        name: Build image job
        steps:
            - name: Checkout master
              uses: actions/checkout@v3
              with:
                fetch-depth: 0
            - name: Get actual patch version
              id: actual_patch_version
              run: echo ::set-output name=ACTUAL_PATCH_VERSION::$(echo $GITHUB_REF | cut -d / -f 3 | sed 's/v//g')
            - name: Get actual minor version
              id: actual_minor_version
              run: echo ::set-output name=ACTUAL_MINOR_VERSION::$(echo $GITHUB_REF | cut -d / -f 3 | sed 's/v//g' | cut -d "." -f 1,2)
            - name: Get actual major version
              id: actual_major_version
              run: echo ::set-output name=ACTUAL_MAJOR_VERSION::$(echo $GITHUB_REF | cut -d / -f 3 | sed 's/v//g' | cut -d "." -f 1)
            - name: Build and publish image
              uses: ilteoood/docker_buildx@1.1.0
              with:
                tag: latest,${{ steps.actual_patch_version.outputs.ACTUAL_PATCH_VERSION }},${{ steps.actual_minor_version.outputs.ACTUAL_MINOR_VERSION }},${{ steps.actual_major_version.outputs.ACTUAL_MAJOR_VERSION }}
                imageName: rclone/rclone
                platform: linux/amd64,linux/386,linux/arm64,linux/arm/v7,linux/arm/v6
                publish: true
                dockerHubUser: ${{ secrets.DOCKER_HUB_USER }}
                dockerHubPassword: ${{ secrets.DOCKER_HUB_PASSWORD }}

    build_docker_volume_plugin:
        if: github.repository == 'rclone/rclone'
        needs: build
        runs-on: ubuntu-latest
        name: Build docker plugin job
        steps:
            - name: Checkout master
              uses: actions/checkout@v3
              with:
                fetch-depth: 0
            - name: Build and publish docker plugin
              shell: bash
              run: |
                VER=${GITHUB_REF#refs/tags/}
                PLUGIN_USER=rclone
                docker login --username ${{ secrets.DOCKER_HUB_USER }} \
                             --password-stdin <<< "${{ secrets.DOCKER_HUB_PASSWORD }}"
                for PLUGIN_ARCH in amd64 arm64 arm/v7 arm/v6 ;do
                    export PLUGIN_USER PLUGIN_ARCH
                    make docker-plugin PLUGIN_TAG=${PLUGIN_ARCH/\//-}
                    make docker-plugin PLUGIN_TAG=${PLUGIN_ARCH/\//-}-${VER#v}
                done
                make docker-plugin PLUGIN_ARCH=amd64 PLUGIN_TAG=latest
                make docker-plugin PLUGIN_ARCH=amd64 PLUGIN_TAG=${VER#v}
