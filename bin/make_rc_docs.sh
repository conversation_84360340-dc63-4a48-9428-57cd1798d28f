#!/bin/bash
# Insert the rc docs into docs/content/rc.md

set -e

go install
mkdir -p /tmp/rclone/cache_test
mkdir -p /tmp/rclone/rc_mount
export RCLONE_CONFIG_RCDOCS_TYPE=cache
export RCLONE_CONFIG_RCDOCS_REMOTE=/tmp/rclone/cache_test
rclone -q --rc mount rcdocs: /tmp/rclone/rc_mount &
sleep 0.5
rclone rc > /tmp/rclone/z.md
fusermount -u -z /tmp/rclone/rc_mount > /dev/null 2>&1 || umount /tmp/rclone/rc_mount

awk '
    BEGIN       {p=1}
    /^\{\{< rem autogenerated start/  {print;system("cat /tmp/rclone/z.md");p=0}
    /^\{\{< rem autogenerated stop/   {p=1}
    p' docs/content/rc.md > /tmp/rclone/rc.md

mv /tmp/rclone/rc.md docs/content/rc.md
rm -rf /tmp/rclone
