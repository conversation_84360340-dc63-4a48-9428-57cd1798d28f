{"importFormats": {"text/tab-separated-values": ["application/vnd.google-apps.spreadsheet"], "application/x-vnd.oasis.opendocument.presentation": ["application/vnd.google-apps.presentation"], "image/jpeg": ["application/vnd.google-apps.document"], "image/bmp": ["application/vnd.google-apps.document"], "image/gif": ["application/vnd.google-apps.document"], "application/vnd.ms-excel.sheet.macroenabled.12": ["application/vnd.google-apps.spreadsheet"], "application/vnd.openxmlformats-officedocument.wordprocessingml.template": ["application/vnd.google-apps.document"], "application/vnd.ms-powerpoint.presentation.macroenabled.12": ["application/vnd.google-apps.presentation"], "application/vnd.ms-word.template.macroenabled.12": ["application/vnd.google-apps.document"], "application/vnd.openxmlformats-officedocument.wordprocessingml.document": ["application/vnd.google-apps.document"], "image/pjpeg": ["application/vnd.google-apps.document"], "application/vnd.google-apps.script+text/plain": ["application/vnd.google-apps.script"], "application/vnd.ms-excel": ["application/vnd.google-apps.spreadsheet"], "application/vnd.sun.xml.writer": ["application/vnd.google-apps.document"], "application/vnd.ms-word.document.macroenabled.12": ["application/vnd.google-apps.document"], "application/vnd.ms-powerpoint.slideshow.macroenabled.12": ["application/vnd.google-apps.presentation"], "text/rtf": ["application/vnd.google-apps.document"], "text/plain": ["application/vnd.google-apps.document"], "application/vnd.oasis.opendocument.spreadsheet": ["application/vnd.google-apps.spreadsheet"], "application/x-vnd.oasis.opendocument.spreadsheet": ["application/vnd.google-apps.spreadsheet"], "image/png": ["application/vnd.google-apps.document"], "application/x-vnd.oasis.opendocument.text": ["application/vnd.google-apps.document"], "application/msword": ["application/vnd.google-apps.document"], "application/pdf": ["application/vnd.google-apps.document"], "application/json": ["application/vnd.google-apps.script"], "application/x-msmetafile": ["application/vnd.google-apps.drawing"], "application/vnd.openxmlformats-officedocument.spreadsheetml.template": ["application/vnd.google-apps.spreadsheet"], "application/vnd.ms-powerpoint": ["application/vnd.google-apps.presentation"], "application/vnd.ms-excel.template.macroenabled.12": ["application/vnd.google-apps.spreadsheet"], "image/x-bmp": ["application/vnd.google-apps.document"], "application/rtf": ["application/vnd.google-apps.document"], "application/vnd.openxmlformats-officedocument.presentationml.template": ["application/vnd.google-apps.presentation"], "image/x-png": ["application/vnd.google-apps.document"], "text/html": ["application/vnd.google-apps.document"], "application/vnd.oasis.opendocument.text": ["application/vnd.google-apps.document"], "application/vnd.openxmlformats-officedocument.presentationml.presentation": ["application/vnd.google-apps.presentation"], "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": ["application/vnd.google-apps.spreadsheet"], "application/vnd.google-apps.script+json": ["application/vnd.google-apps.script"], "application/vnd.openxmlformats-officedocument.presentationml.slideshow": ["application/vnd.google-apps.presentation"], "application/vnd.ms-powerpoint.template.macroenabled.12": ["application/vnd.google-apps.presentation"], "text/csv": ["application/vnd.google-apps.spreadsheet"], "application/vnd.oasis.opendocument.presentation": ["application/vnd.google-apps.presentation"], "image/jpg": ["application/vnd.google-apps.document"], "text/richtext": ["application/vnd.google-apps.document"]}, "exportFormats": {"application/vnd.google-apps.document": ["application/rtf", "application/vnd.oasis.opendocument.text", "text/html", "application/pdf", "application/epub+zip", "application/zip", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "text/plain"], "application/vnd.google-apps.spreadsheet": ["application/x-vnd.oasis.opendocument.spreadsheet", "text/tab-separated-values", "application/pdf", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "text/csv", "application/zip", "application/vnd.oasis.opendocument.spreadsheet"], "application/vnd.google-apps.jam": ["application/pdf"], "application/vnd.google-apps.script": ["application/vnd.google-apps.script+json"], "application/vnd.google-apps.presentation": ["application/vnd.oasis.opendocument.presentation", "application/pdf", "application/vnd.openxmlformats-officedocument.presentationml.presentation", "text/plain"], "application/vnd.google-apps.form": ["application/zip"], "application/vnd.google-apps.drawing": ["image/svg+xml", "image/png", "application/pdf", "image/jpeg"]}}