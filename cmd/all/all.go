// Package all imports all the commands
package all

import (
	// Active commands
	_ "github.com/rclone/rclone/cmd"
	_ "github.com/rclone/rclone/cmd/about"
	_ "github.com/rclone/rclone/cmd/authorize"
	_ "github.com/rclone/rclone/cmd/backend"
	_ "github.com/rclone/rclone/cmd/bisync"
	_ "github.com/rclone/rclone/cmd/cachestats"
	_ "github.com/rclone/rclone/cmd/cat"
	_ "github.com/rclone/rclone/cmd/check"
	_ "github.com/rclone/rclone/cmd/checksum"
	_ "github.com/rclone/rclone/cmd/cleanup"
	_ "github.com/rclone/rclone/cmd/cmount"
	_ "github.com/rclone/rclone/cmd/config"
	_ "github.com/rclone/rclone/cmd/copy"
	_ "github.com/rclone/rclone/cmd/copyto"
	_ "github.com/rclone/rclone/cmd/copyurl"
	_ "github.com/rclone/rclone/cmd/cryptcheck"
	_ "github.com/rclone/rclone/cmd/cryptdecode"
	_ "github.com/rclone/rclone/cmd/dedupe"
	_ "github.com/rclone/rclone/cmd/delete"
	_ "github.com/rclone/rclone/cmd/deletefile"
	_ "github.com/rclone/rclone/cmd/genautocomplete"
	_ "github.com/rclone/rclone/cmd/gendocs"
	_ "github.com/rclone/rclone/cmd/hashsum"
	_ "github.com/rclone/rclone/cmd/link"
	_ "github.com/rclone/rclone/cmd/listremotes"
	_ "github.com/rclone/rclone/cmd/ls"
	_ "github.com/rclone/rclone/cmd/lsd"
	_ "github.com/rclone/rclone/cmd/lsf"
	_ "github.com/rclone/rclone/cmd/lsjson"
	_ "github.com/rclone/rclone/cmd/lsl"
	_ "github.com/rclone/rclone/cmd/md5sum"
	_ "github.com/rclone/rclone/cmd/mkdir"
	_ "github.com/rclone/rclone/cmd/mount"
	_ "github.com/rclone/rclone/cmd/mount2"
	_ "github.com/rclone/rclone/cmd/move"
	_ "github.com/rclone/rclone/cmd/moveto"
	_ "github.com/rclone/rclone/cmd/ncdu"
	_ "github.com/rclone/rclone/cmd/obscure"
	_ "github.com/rclone/rclone/cmd/purge"
	_ "github.com/rclone/rclone/cmd/rc"
	_ "github.com/rclone/rclone/cmd/rcat"
	_ "github.com/rclone/rclone/cmd/rcd"
	_ "github.com/rclone/rclone/cmd/reveal"
	_ "github.com/rclone/rclone/cmd/rmdir"
	_ "github.com/rclone/rclone/cmd/rmdirs"
	_ "github.com/rclone/rclone/cmd/selfupdate"
	_ "github.com/rclone/rclone/cmd/serve"
	_ "github.com/rclone/rclone/cmd/settier"
	_ "github.com/rclone/rclone/cmd/sha1sum"
	_ "github.com/rclone/rclone/cmd/size"
	_ "github.com/rclone/rclone/cmd/sync"
	_ "github.com/rclone/rclone/cmd/test"
	_ "github.com/rclone/rclone/cmd/test/changenotify"
	_ "github.com/rclone/rclone/cmd/test/histogram"
	_ "github.com/rclone/rclone/cmd/test/info"
	_ "github.com/rclone/rclone/cmd/test/makefiles"
	_ "github.com/rclone/rclone/cmd/test/memory"
	_ "github.com/rclone/rclone/cmd/touch"
	_ "github.com/rclone/rclone/cmd/tree"
	_ "github.com/rclone/rclone/cmd/version"
)
