test extended-char-paths
# Tests handling of extended UTF-8 chars in:
#   path1, path2
#   --check-filename
#   --filters-file (path and filename)
#
# Not tested:
#   --workdir (hardcoded by test engine)
#   tab in subdir (currently using '{spc}_{spc}' instead of '{tab}_{spc}')
#
# Notes:
# - Github windows runner fails to checkout repo with tabs in names.
# - Due to different encoding local backend returns the literal tab
#   in logs or listings, but remotes return two ASCII chars `\t`.

test resync subdirs with extended chars
bisync subdir=測試_Русский_{spc}_{spc}_ě_áñ resync
copy-listings resync

test place new files with extended chars on each side
# force specific modification time since file time is lost through git
touch-glob 2001-01-02 {datadir/} file1.txt
copy-as {datadir/}file1.txt {path1/}測試_Русский_{spc}_{spc}_ě_áñ 測試_file1p1
copy-as {datadir/}file1.txt {path2/}測試_Русский_{spc}_{spc}_ě_áñ 測試_file1p2

test normal sync of subdirs with extended chars
bisync subdir=測試_Русский_{spc}_{spc}_ě_áñ
move-listings normal-sync

test check-filename with extended chars. check should fail.
bisync resync
delete-file {path1/}測試_Русский_{spc}_{spc}_ě_áñ/測試_check{spc}file
bisync check-access check-filename=測試_check{spc}file
copy-listings check-access-fail

test check-filename with extended chars. check should pass.
bisync resync
bisync check-access check-filename=測試_check{spc}file
move-listings check-access-pass

test filters-file path with extended chars - masks /fileZ.txt
copy-file {datadir/}測試_filtersfile.txt {workdir/}
bisync filters-file={workdir/}測試_filtersfile.txt resync
copy-as {datadir/}file1.txt {path1/} fileZ.txt
bisync filters-file={workdir/}測試_filtersfile.txt
