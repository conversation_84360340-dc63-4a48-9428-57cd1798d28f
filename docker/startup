#!/bin/sh

RCLONE_CONFIG=/config/rclone/rclone.conf

if [ -z "$REMOTE_NAME" ]; then
    echo "REMOTE_NAME is not set"
    exit 1
fi
if [ -z "$REMOTE_URL" ]; then
    echo "REMOTE_URL is not set"
    exit 1
fi
if [ -z "$REMOTE_VENDOR" ]; then
    echo "REMOTE_VENDOR is not set"
    exit 1
fi

mkdir -p "$(dirname "$RCLONE_CONFIG")"

cat <<EOF >"$RCLONE_CONFIG"
[$REMOTE_NAME]
type = webdav
url = $REMOTE_URL
vendor = $REMOTE_VENDOR
nextcloud_chunk_size = 0
EOF

# parse args
ifsBak=$IFS
IFS=' '

# Check if PROXY_ARGS contains --auth-proxy
if [[ "$PROXY_ARGS" == *"--auth-proxy"* ]]; then
    # Auth proxy is being used, don't include remote name
    ARGS=""
else
    # No auth proxy, include remote name
    ARGS="$REMOTE_NAME:"
fi

for arg in $PROXY_ARGS; do
    ARGS="$ARGS $arg"
done

IFS=$ifsBak

# DO NOT QUOTE ARGS
rclone serve s3 ${ARGS}
