name: Docker beta build

on:
    push:
      branches:
        - master
jobs:
    build:
        if: github.repository == 'rclone/rclone'
        runs-on: ubuntu-latest
        name: Build image job
        steps:
            - name: Checkout master
              uses: actions/checkout@v3
              with:
                fetch-depth: 0
            - name: Login to Docker Hub
              uses: docker/login-action@v2
              with:
                username: ${{ secrets.DOCKERHUB_USERNAME }}
                password: ${{ secrets.DOCKERHUB_TOKEN }}
            - name: Extract metadata (tags, labels) for Docker
              id: meta
              uses: docker/metadata-action@v4
              with:
                images: ghcr.io/${{ github.repository }}
            - name: Set up QEMU
              uses: docker/setup-qemu-action@v2
            - name: Set up Docker Buildx
              uses: docker/setup-buildx-action@v2
            - name: Login to GitHub Container Registry
              uses: docker/login-action@v2
              with:
                registry: ghcr.io
                # This is the user that triggered the Workflow. In this case, it will
                # either be the user whom created the Release or manually triggered
                # the workflow_dispatch.
                username: ${{ github.actor }}
                # `secrets.GITHUB_TOKEN` is a secret that's automatically generated by
                # GitHub Actions at the start of a workflow run to identify the job.
                # This is used to authenticate against GitHub Container Registry.
                # See https://docs.github.com/en/actions/security-guides/automatic-token-authentication#about-the-github_token-secret
                # for more detailed information.
                password: ${{ secrets.GITHUB_TOKEN }}

            - name: Build and publish image
              uses: docker/build-push-action@v4
              with:
                file: Dockerfile
                context: .
                push: true # push the image to ghcr
                tags: |
                  ghcr.io/rclone/rclone:beta
                  rclone/rclone:beta
                labels: ${{ steps.meta.outputs.labels }}
                platforms: linux/amd64,linux/386,linux/arm64,linux/arm/v7,linux/arm/v6
                cache-from: type=gha
                cache-to: type=gha,mode=max
                provenance: false
              # Eventually cache will need to be cleared if builds more frequent than once a week
              # https://github.com/docker/build-push-action/issues/252
