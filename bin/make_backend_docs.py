#!/usr/bin/env python3
"""
Make backend documentation
"""

import sys
import os
import io
import subprocess
from pathlib import Path

marker = "{{< rem autogenerated options"
start = marker + " start"
stop = marker + " stop"
end = ">}}"

def find_backends():
    """Return a list of all backends"""
    return [ x for x in os.listdir("backend") if x not in ("all",) ]

def output_docs(backend, out, cwd):
    """Output documentation for backend options to out"""
    out.flush()
    subprocess.check_call(["./rclone", "help", "backend", backend], stdout=out)

def output_backend_tool_docs(backend, out, cwd):
    """Output documentation for backend tool to out"""
    out.flush()
    subprocess.call(["./rclone", "backend", "help", backend], stdout=out, stderr=subprocess.DEVNULL)
    
def alter_doc(backend):
    """Alter the documentation for backend"""
    rclone_bin_dir = Path(sys.path[0]).parent.absolute()
    doc_file = "docs/content/"+backend+".md"
    if not os.path.exists(doc_file):
        raise ValueError("Didn't find doc file %s" % (doc_file,))
    new_file = doc_file+"~new~"
    altered = False
    with open(doc_file, "r", encoding="utf_8") as in_file, open(new_file, "w", encoding="utf_8") as out_file:
        in_docs = False
        for line in in_file:
            if not in_docs:
                if start in line:
                    in_docs = True
                    start_full = (start + "\" - DO NOT EDIT - instead edit fs.RegInfo in backend/%s/%s.go then run make backenddocs\" " + end + "\n") % (backend, backend)
                    out_file.write(start_full)
                    output_docs(backend, out_file, rclone_bin_dir)
                    output_backend_tool_docs(backend, out_file, rclone_bin_dir)
                    out_file.write(stop+" "+end+"\n")
                    altered = True
            if not in_docs:
                out_file.write(line)
            if in_docs:
                if stop in line:
                    in_docs = False
    os.rename(doc_file, doc_file+"~")
    os.rename(new_file, doc_file)
    if not altered:
        raise ValueError("Didn't find '%s' markers for in %s" % (start, doc_file))


def main(args):
    # single backend
    if (len(args) == 2):
        try:
            alter_doc(args[1])
            print("Added docs for %s backend" % args[1])
        except Exception as e:
            print("Failed adding docs for %s backend: %s" % (args[1], e))
        return

    # all backends
    failed, success = 0, 0
    for backend in find_backends():
        try:
            alter_doc(backend)
        except Exception as e:
            print("Failed adding docs for %s backend: %s" % (backend, e))
            failed += 1
        else:
            success += 1
    print("Added docs for %d backends with %d failures" % (success, failed))

if __name__ == "__main__":
    main(sys.argv)
